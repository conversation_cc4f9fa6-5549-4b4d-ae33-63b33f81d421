import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/alert_service.dart';
import '../models/government_alert.dart';

// Enhanced provider for alerts with mock data
final alertsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  await Future.delayed(const Duration(seconds: 1));

  return [
    {
      'id': '1',
      'title': 'Flood Warning',
      'message': 'Heavy rainfall expected in the next 24 hours. Residents in low-lying areas should evacuate immediately.',
      'severity': 'high',
      'type': 'flood',
      'timestamp': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
      'isActive': true,
      'affectedAreas': ['North District', 'River Valley', 'Downtown'],
    },
    {
      'id': '2',
      'title': 'Emergency Shelter Available',
      'message': 'New emergency shelter opened at Central Community Hall. Food and medical aid available.',
      'severity': 'medium',
      'type': 'shelter',
      'timestamp': DateTime.now().subtract(const Duration(hours: 4)).toIso8601String(),
      'isActive': true,
      'affectedAreas': ['City Center'],
    },
    {
      'id': '3',
      'title': 'Road Closure Alert',
      'message': 'Highway 101 closed due to landslide. Use alternate routes via Highway 205.',
      'severity': 'medium',
      'type': 'traffic',
      'timestamp': DateTime.now().subtract(const Duration(hours: 6)).toIso8601String(),
      'isActive': true,
      'affectedAreas': ['Highway 101', 'North Route'],
    },
    {
      'id': '4',
      'title': 'Weather Update',
      'message': 'Storm warning lifted. Weather conditions improving. Normal activities can resume.',
      'severity': 'low',
      'type': 'weather',
      'timestamp': DateTime.now().subtract(const Duration(hours: 8)).toIso8601String(),
      'isActive': false,
      'affectedAreas': ['All Areas'],
    },
  ];
});

class AlertsScreen extends ConsumerWidget {
  const AlertsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final alertsAsync = ref.watch(alertsProvider);

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Emergency Alerts',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.brown.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context, ref),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.refresh(alertsProvider),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.refresh(alertsProvider.future),
        child: alertsAsync.when(
          loading: () => const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading alerts...'),
              ],
            ),
          ),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
                const SizedBox(height: 16),
                Text('Error loading alerts'),
                const SizedBox(height: 8),
                Text('Please check your connection and try again'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(alertsProvider),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
          data: (alerts) => _buildAlertsList(context, alerts),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showCreateAlertDialog(context),
        backgroundColor: Colors.orange.shade600,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add_alert),
        label: const Text('Report Issue'),
      ),
    );
  }

  Widget _buildAlertsList(BuildContext context, List<Map<String, dynamic>> alerts) {
    if (alerts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.notifications_none, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No active alerts'),
            SizedBox(height: 8),
            Text('You will be notified when emergency alerts are issued'),
          ],
        ),
      );
    }

    final activeAlerts = alerts.where((alert) => alert['isActive'] == true).toList();
    final inactiveAlerts = alerts.where((alert) => alert['isActive'] == false).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAlertsSummary(alerts),
          const SizedBox(height: 20),
          if (activeAlerts.isNotEmpty) ...[
            _buildSectionHeader('Active Alerts', activeAlerts.length, Colors.red),
            const SizedBox(height: 12),
            ...activeAlerts.map((alert) => _buildAlertCard(context, alert, true)),
            const SizedBox(height: 20),
          ],
          if (inactiveAlerts.isNotEmpty) ...[
            _buildSectionHeader('Recent Updates', inactiveAlerts.length, Colors.green),
            const SizedBox(height: 12),
            ...inactiveAlerts.map((alert) => _buildAlertCard(context, alert, false)),
          ],
        ],
      ),
    );
  }

  Widget _buildAlertsSummary(List<Map<String, dynamic>> alerts) {
    final activeCount = alerts.where((alert) => alert['isActive'] == true).length;
    final highSeverityCount = alerts.where((alert) => alert['severity'] == 'high').length;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.dashboard, color: Colors.brown.shade600),
                const SizedBox(width: 8),
                Text(
                  'Alert Summary',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.brown.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Active Alerts',
                    activeCount.toString(),
                    Icons.warning,
                    activeCount > 0 ? Colors.red : Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'High Priority',
                    highSeverityCount.toString(),
                    Icons.priority_high,
                    highSeverityCount > 0 ? Colors.orange : Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, int count, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(Icons.circle, color: color, size: 12),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.brown.shade800,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            count.toString(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAlertCard(BuildContext context, Map<String, dynamic> alert, bool isActive) {
    final severity = alert['severity'] as String;
    final type = alert['type'] as String;
    final timestamp = DateTime.parse(alert['timestamp']);
    final timeAgo = _getTimeAgo(timestamp);

    Color severityColor;
    IconData alertIcon;

    switch (severity) {
      case 'high':
        severityColor = Colors.red;
        alertIcon = Icons.warning;
        break;
      case 'medium':
        severityColor = Colors.orange;
        alertIcon = Icons.info;
        break;
      case 'low':
        severityColor = Colors.blue;
        alertIcon = Icons.info_outline;
        break;
      default:
        severityColor = Colors.grey;
        alertIcon = Icons.notifications;
    }

    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showAlertDetails(context, alert),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: severityColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(alertIcon, color: severityColor, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          alert['title'],
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          type.toUpperCase(),
                          style: TextStyle(
                            fontSize: 12,
                            color: severityColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isActive ? Colors.red.shade100 : Colors.green.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          isActive ? 'ACTIVE' : 'RESOLVED',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: isActive ? Colors.red.shade700 : Colors.green.shade700,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        timeAgo,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                alert['message'],
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade700,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (alert['affectedAreas'] != null) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 6,
                  children: (alert['affectedAreas'] as List<String>).take(3).map((area) =>
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        area,
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _showAlertDetails(BuildContext context, Map<String, dynamic> alert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(alert['title']),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Type: ${alert['type'].toString().toUpperCase()}'),
              const SizedBox(height: 8),
              Text('Severity: ${alert['severity'].toString().toUpperCase()}'),
              const SizedBox(height: 8),
              Text('Status: ${alert['isActive'] ? 'ACTIVE' : 'RESOLVED'}'),
              const SizedBox(height: 16),
              Text(alert['message']),
              if (alert['affectedAreas'] != null) ...[
                const SizedBox(height: 16),
                Text('Affected Areas:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...(alert['affectedAreas'] as List<String>).map((area) =>
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text('• $area'),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (alert['isActive'])
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Marked as safe from: ${alert['title']}')),
                );
              },
              child: const Text('Mark Safe'),
            ),
        ],
      ),
    );
  }

  void _showCreateAlertDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Issue'),
        content: const Text('This feature allows you to report emergencies or issues to local authorities.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Issue reported to authorities')),
              );
            },
            child: const Text('Report'),
          ),
        ],
      ),
    );
  }

  static void _showFilterDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Alerts'),
        content: const Text('Filter options coming soon'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
