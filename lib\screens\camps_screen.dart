import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/camps_service.dart';
import '../models/camp.dart';

// Mock data provider for camps
final campsProvider = FutureProvider<List<Camp>>((ref) async {
  // Simulate API call delay
  await Future.delayed(const Duration(seconds: 1));

  return Camp.mockList();
});

class CampsScreen extends ConsumerWidget {
  const CampsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final campsAsync = ref.watch(campsProvider);

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Relief Camps',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.brown.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.map),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Map view coming soon')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.refresh(campsProvider),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.refresh(campsProvider.future),
        child: campsAsync.when(
          loading: () => const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading relief camps...'),
              ],
            ),
          ),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
                const SizedBox(height: 16),
                Text('Error loading camps: $error'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(campsProvider),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
          data: (camps) => _buildCampsList(context, camps),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Emergency camp request sent')),
          );
        },
        backgroundColor: Colors.red.shade600,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.emergency),
        label: const Text('Request Help'),
      ),
    );
  }

  Widget _buildCampsList(BuildContext context, List<Camp> camps) {
    if (camps.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.cabin, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No relief camps available'),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard(camps),
          const SizedBox(height: 16),
          Text(
            'Available Camps',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.brown.shade800,
            ),
          ),
          const SizedBox(height: 12),
          ...camps.map((camp) => _buildCampCard(context, camp)),
        ],
      ),
    );
  }

  Widget _buildStatsCard(List<Camp> camps) {
    final totalCapacity = camps.fold<int>(0, (sum, camp) => sum + camp.capacity);
    final totalOccupancy = camps.fold<int>(0, (sum, camp) => sum + camp.currentOccupancy);
    final availableSpace = totalCapacity - totalOccupancy;
    final occupancyPercentage = totalCapacity > 0 ? (totalOccupancy / totalCapacity * 100).round() : 0;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.brown.shade600),
                const SizedBox(width: 8),
                Text(
                  'Camp Statistics',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.brown.shade800,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Camps',
                    camps.length.toString(),
                    Icons.cabin,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Available Space',
                    availableSpace.toString(),
                    Icons.people,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Capacity',
                    totalCapacity.toString(),
                    Icons.home,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Occupancy',
                    '$occupancyPercentage%',
                    Icons.trending_up,
                    occupancyPercentage > 80 ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCampCard(BuildContext context, Camp camp) {
    final occupancyPercentage = (camp.currentOccupancy / camp.capacity * 100).round();
    final isNearFull = occupancyPercentage >= 80;

    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          _showCampDetails(context, camp);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getCampTypeColor(camp.type).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getCampTypeIcon(camp.type),
                      color: _getCampTypeColor(camp.type),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          camp.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          camp.address,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: isNearFull ? Colors.red.shade100 : Colors.green.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      camp.occupancyStatus,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: isNearFull ? Colors.red.shade700 : Colors.green.shade700,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.people, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    '${camp.currentOccupancy}/${camp.capacity}',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    '${camp.latitude.toStringAsFixed(2)}, ${camp.longitude.toStringAsFixed(2)}',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildFacilitiesRow(camp),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFacilitiesRow(Camp camp) {
    final facilities = ['food', 'water', 'medical', 'shelter'];

    return Row(
      children: facilities.map((facility) {
        final hasResource = camp.hasResource(facility);
        final status = hasResource ? camp.getResourceStatus(facility) : 'N/A';
        final color = _getFacilityColor(status);

        return Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 2),
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              children: [
                Icon(
                  _getFacilityIcon(facility),
                  size: 16,
                  color: color,
                ),
                const SizedBox(height: 2),
                Text(
                  facility.toUpperCase(),
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Color _getCampTypeColor(String type) {
    switch (type) {
      case 'evacuation_center':
        return Colors.blue;
      case 'medical_camp':
        return Colors.red;
      case 'food_distribution':
        return Colors.orange;
      case 'shelter':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getCampTypeIcon(String type) {
    switch (type) {
      case 'evacuation_center':
        return Icons.emergency;
      case 'medical_camp':
        return Icons.medical_services;
      case 'food_distribution':
        return Icons.restaurant;
      case 'shelter':
        return Icons.home;
      default:
        return Icons.cabin;
    }
  }

  Color _getFacilityColor(String status) {
    switch (status.toLowerCase()) {
      case 'good':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getFacilityIcon(String facility) {
    switch (facility) {
      case 'food':
        return Icons.restaurant;
      case 'water':
        return Icons.water_drop;
      case 'medical':
        return Icons.medical_services;
      case 'shelter':
        return Icons.home;
      default:
        return Icons.help;
    }
  }

  void _showCampDetails(BuildContext context, Camp camp) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(camp.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Address: ${camp.address}'),
              const SizedBox(height: 8),
              Text('Type: ${camp.type.replaceAll('_', ' ').toUpperCase()}'),
              const SizedBox(height: 8),
              Text('Capacity: ${camp.currentOccupancy}/${camp.capacity}'),
              const SizedBox(height: 8),
              Text('Status: ${camp.occupancyStatus}'),
              const SizedBox(height: 8),
              Text('Contact: ${camp.createdBy}'),
              const SizedBox(height: 16),
              Text(
                'Facilities:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...camp.facilities.entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Icon(
                      _getFacilityIcon(entry.key),
                      size: 16,
                      color: _getFacilityColor(entry.value['status']),
                    ),
                    const SizedBox(width: 8),
                    Text('${entry.key}: ${entry.value['status']} (${entry.value['quantity']}%)'),
                  ],
                ),
              )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Requested admission to ${camp.name}')),
              );
            },
            child: const Text('Request Admission'),
          ),
        ],
      ),
    );
  }
}